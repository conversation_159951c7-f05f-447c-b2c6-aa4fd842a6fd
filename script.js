// 全局变量
let selectedFile = null;
let workbookData = null;

// DOM元素引用
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const convertBtn = document.getElementById('convertBtn');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const messageArea = document.getElementById('messageArea');

// 初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // 文件选择事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    // 转换按钮事件
    convertBtn.addEventListener('click', handleConvert);
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndProcessFile(file);
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放置
function handleFileDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        validateAndProcessFile(files[0]);
    }
}

// 验证和处理文件
function validateAndProcessFile(file) {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
        showMessage('请选择 .xlsx 格式的Excel文件', 'error');
        return;
    }
    
    // 验证文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
        showMessage('文件大小不能超过10MB', 'error');
        return;
    }
    
    selectedFile = file;
    displayFileInfo(file);
    convertBtn.disabled = false;
    showMessage('文件选择成功，可以开始转换', 'success');
}

// 显示文件信息
function displayFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileDate').textContent = new Date(file.lastModified).toLocaleString('zh-CN');
    
    fileInfo.style.display = 'block';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 处理转换
async function handleConvert() {
    if (!selectedFile) {
        showMessage('请先选择文件', 'error');
        return;
    }
    
    try {
        // 禁用转换按钮
        convertBtn.disabled = true;
        convertBtn.textContent = '转换中...';
        
        // 显示进度
        showProgress(true);
        updateProgress(10, '正在读取文件...');
        
        // 读取文件
        const arrayBuffer = await readFileAsArrayBuffer(selectedFile);
        updateProgress(30, '正在解析Excel文件...');
        
        // 解析Excel文件
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        updateProgress(50, '正在处理数据...');
        
        // 处理数据
        const processedData = processWorkbookData(workbook);
        updateProgress(70, '正在生成新文件...');
        
        // 生成新的Excel文件
        const newWorkbook = createFormattedWorkbook(processedData);
        updateProgress(90, '正在准备下载...');
        
        // 下载文件
        downloadWorkbook(newWorkbook);
        updateProgress(100, '转换完成！');
        
        showMessage('文件转换成功！已自动下载', 'success');
        
    } catch (error) {
        console.error('转换过程中发生错误:', error);
        showMessage('转换失败: ' + error.message, 'error');
    } finally {
        // 重置按钮状态
        setTimeout(() => {
            convertBtn.disabled = false;
            convertBtn.textContent = '开始转换';
            showProgress(false);
        }, 2000);
    }
}

// 读取文件为ArrayBuffer
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('文件读取失败'));
        reader.readAsArrayBuffer(file);
    });
}

// 处理工作簿数据
function processWorkbookData(workbook) {
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // 将工作表转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (jsonData.length === 0) {
        throw new Error('Excel文件为空或格式不正确');
    }
    
    // 获取表头
    const headers = jsonData[0];
    if (!headers || headers.length === 0) {
        throw new Error('无法读取表头信息');
    }
    
    // 过滤掉'记录人'列
    const filteredHeaders = [];
    const columnIndexMap = [];
    
    headers.forEach((header, index) => {
        if (header !== '记录人') {
            filteredHeaders.push(header);
            columnIndexMap.push(index);
        }
    });
    
    // 添加新列
    filteredHeaders.push('备注', '维护保养情况');
    
    // 处理数据行
    const processedRows = [];
    for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row.length > 0) {
            const newRow = [];
            
            // 复制过滤后的列数据
            columnIndexMap.forEach(colIndex => {
                newRow.push(row[colIndex] || '');
            });
            
            // 添加新列数据
            newRow.push('已解决'); // 备注列
            newRow.push(''); // 维护保养情况列
            
            processedRows.push(newRow);
        }
    }
    
    return {
        headers: filteredHeaders,
        rows: processedRows
    };
}

// 创建格式化的工作簿
function createFormattedWorkbook(data) {
    // 创建新工作簿
    const newWorkbook = XLSX.utils.book_new();
    
    // 准备数据
    const worksheetData = [data.headers, ...data.rows];
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(newWorkbook, worksheet, '运维日志');
    
    return newWorkbook;
}

// 下载工作簿
function downloadWorkbook(workbook) {
    // 生成文件名
    const today = new Date();
    const dateStr = today.getFullYear() + 
                   String(today.getMonth() + 1).padStart(2, '0') + 
                   String(today.getDate()).padStart(2, '0');
    const fileName = `科陆流水线运维日志${dateStr}.xlsx`;
    
    // 生成文件并下载
    XLSX.writeFile(workbook, fileName);
}

// 显示消息
function showMessage(text, type = 'info') {
    messageArea.textContent = text;
    messageArea.className = `message ${type}`;
    messageArea.style.display = 'block';
    
    // 3秒后自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            messageArea.style.display = 'none';
        }, 3000);
    }
}

// 显示/隐藏进度
function showProgress(show) {
    progressSection.style.display = show ? 'block' : 'none';
    if (!show) {
        progressFill.style.width = '0%';
    }
}

// 更新进度
function updateProgress(percent, text) {
    progressFill.style.width = percent + '%';
    progressText.textContent = text;
}
